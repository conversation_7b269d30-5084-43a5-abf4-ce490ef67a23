import { useEffect } from 'react'
import { useRouter } from '@/i18n/routing'
import useAuthStore from '@/store/auth'
import { UserRole } from '@/types/auth'

export const useAuth = () => {
  const store = useAuthStore()
  return store
}

// 注意：useRequireAuth 和 useRequireRole 已被新的权限系统替代
// 请使用 PageGuard 组件或 usePageAuth Hook 替代这些函数
// 这些函数将在下个版本中移除

export const useLogout = () => {
  const logout = useAuthStore(state => state.logout)
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  return handleLogout
} 