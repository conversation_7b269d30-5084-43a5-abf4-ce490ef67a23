import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthStore, LoginRequest, User } from '@/types/auth'
import { authApi, ApiError } from '@/lib/api'
import { errorHandler, ErrorType } from '@/lib/error-handler'

const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true, // set to true to avoid infinite loop
      error: null,
      
      _initialized: false,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApi.login(credentials)
          const { token, ...user } = response
          
          // save to localStorage when window is defined
          if (typeof window !== 'undefined') {
            localStorage.setItem('auth_token', token)
          }
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          const result = errorHandler.handleApiError(error)

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: result.message,
          })

          // 如果需要登出，清除认证状态
          if (result.shouldLogout) {
            get().logout()
          }

          throw error
        }
      },

      logout: () => {
        // remove auth_token from localStorage when window is defined
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token')
        }
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          _initialized: true, // 保持 initialized 状态，避免重新初始化
        })
      },

      setUser: (user: User) => {
        set({ user })
      },

      setToken: (token: string) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', token)
        }
        set({ token })
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      initializeAuth: async () => {
        const currentState = get()

        // if already initialized, return
        if (currentState._initialized) {
          return
        }

        set({ isLoading: true })

        try {
          if (typeof window !== 'undefined') {
            const token = localStorage.getItem('auth_token')
            if (token) {
              // verify token and get user info
              const user = await authApi.getProfile()
              set({
                token,
                user,
                isAuthenticated: true,
                isLoading: false,
                _initialized: true,
              })
            } else {
              set({
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false,
                _initialized: true,
              })
            }
          } else {
            set({
              isLoading: false,
              _initialized: true,
            })
          }
        } catch (error) {
          const result = errorHandler.handleApiError(error)

          // if token is invalid, clear authentication state
          if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token')
          }

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            _initialized: true,
            error: result.message,
          })

          // 静默处理初始化错误，不显示toast
          // 因为这通常是token过期的正常情况
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        // do not persist _initialized and isLoading
      }),
      onRehydrateStorage: () => (state) => {
        // keep _initialized to false to trigger reinitialization
        // but do not reset isLoading, let initializeAuth manage it
        if (state) {
          state._initialized = false
          // keep isLoading to true to avoid infinite loop
        }
      },
    }
  )
)

export default useAuthStore 